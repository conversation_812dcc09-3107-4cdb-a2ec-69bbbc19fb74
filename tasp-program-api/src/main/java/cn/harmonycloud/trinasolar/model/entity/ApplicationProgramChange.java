package cn.harmonycloud.trinasolar.model.entity;

import cn.harmonycloud.common.core.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * @className: ApplicationProgramChange
 * @Description: 应用程序变更记录实体类
 * 对应数据库表：application_program_change
 * @author: pengshy
 * @date: 2025/9/2 15:36
 */
@Builder
@TableName(value = "application_program_change", autoResultMap = true)
public class ApplicationProgramChange extends BaseEntity {
    /**
     * 应用程序ID
     */
    private String appId;

    /**
     * 应用程序编码
     */
    private String appCode;

    /**
     * 所属系统ID
     */
    private String systemId;

    /**
     * 变更类型(1:新增,2:更新,3:删除)
     */
    private Integer changeType;

    /**
     * 变更后版本号
     */
    private Integer currentVersion;

    /**
     * 变更后数据(JSON)
     */
    private String currentData;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 变更时间
     */
    private LocalDateTime changeTime;

    /**
     * 是否已处理(0:否,1:是)
     */
    private Integer isProcessed;
}
