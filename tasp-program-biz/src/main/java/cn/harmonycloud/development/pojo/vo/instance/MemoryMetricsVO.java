package cn.harmonycloud.development.pojo.vo.instance;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 内存指标数据VO
 */
@Data
@ApiModel(description = "内存指标数据")
public class MemoryMetricsVO {

    @ApiModelProperty(value = "实例名称")
    private String podName;

    @ApiModelProperty(value = "内存使用数据点列表")
    private List<MemoryDataPoint> dataPoints;

    @ApiModelProperty(value = "时间范围开始时间")
    private Long startTime;

    @ApiModelProperty(value = "时间范围结束时间")
    private Long endTime;

    /**
     * 内存数据点
     */
    @Data
    @ApiModel(description = "内存数据点")
    public static class MemoryDataPoint {

        @ApiModelProperty(value = "时间戳，精确到秒")
        private Long timestamp;

        @ApiModelProperty(value = "内存使用量，单位MB")
        private Double memoryUsageMB;

        @ApiModelProperty(value = "时间格式化字符串，格式为yyyy/MM/dd HH:mm:ss")
        private String timeString;
    }
}
