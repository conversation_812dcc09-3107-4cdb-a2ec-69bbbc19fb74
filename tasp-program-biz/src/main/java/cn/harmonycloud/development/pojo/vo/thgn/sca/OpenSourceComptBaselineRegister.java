package cn.harmonycloud.development.pojo.vo.thgn.sca;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 开源组件基线表登记响应VO
 * 对应数据库表：register_open_source_component
 * <AUTHOR>
 */
@Schema(description = "开源组件基线登记VO")
@Data
@TableName("register_open_source_component") // 表名映射
public class OpenSourceComptBaselineRegister {
    /**
     * 唯一标识（主键）
     */
    @Schema(description = "唯一标识（主键）", example = "1")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 应用程序id
     */
    @Schema(description = "应用程序id", example = "1001")
    private Long appId;

    /**
     * 组件名称
     */
    @Schema(description = "组件名称", required = true, example = "Spring Boot")
    private String name;

    /**
     * 基准版本
     */
    @Schema(description = "基准版本", required = true, example = "2.7.0")
    private String version;

    /**
     * 使用版本
     */
    @Schema(description = "使用版本", required = true, example = "2.7.8")
    private String useVersion;

    /**
     * 是否为基线组件（0不是|1是）
     */
    @Schema(description = "是否为基线组件（0不是|1是）", required = true, example = "1")
    private Integer isBaseline;

    /**
     * 是否启用（0关闭|1启用）
     */
    @Schema(description = "是否启用（0关闭|1启用）", required = true, example = "1")
    private Integer isUsed;

    /**
     * 描述
     */
    @Schema(description = "描述", example = "企业级应用开发框架")
    private String description;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2023-01-01 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2023-01-01 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @Schema(description = "创建人ID", example = "1001")
    private Long createBy;

    /**
     * 更新人
     */
    @Schema(description = "更新人ID", example = "1001")
    private Long updateBy;

    /**
     * 删除标识（0-未删除，1-已删除）
     */
    @Schema(description = "删除标识（0-未删除，1-已删除）", example = "0")
    @TableLogic
    private Integer delFlag;
}
