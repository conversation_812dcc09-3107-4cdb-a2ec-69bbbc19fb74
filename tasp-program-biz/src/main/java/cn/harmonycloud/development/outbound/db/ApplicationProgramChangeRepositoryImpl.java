package cn.harmonycloud.development.outbound.db;

import cn.harmonycloud.development.outbound.db.mapper.thgn.ApplicationProgramChangeMapper;
import cn.harmonycloud.development.outbound.trinasolar.ApplicationProgramChangeRepository;
import cn.harmonycloud.trinasolar.model.entity.ApplicationProgramChange;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * @className: ApplicationProgramChangeRepositoryImpl
 * @Description: 应用程序变更记录Repository
 * @author: pengshy
 * @date: 2025/9/2 15:45
 */
@Service
public class ApplicationProgramChangeRepositoryImpl extends ServiceImpl<ApplicationProgramChangeMapper, ApplicationProgramChange>
        implements ApplicationProgramChangeRepository {
}
