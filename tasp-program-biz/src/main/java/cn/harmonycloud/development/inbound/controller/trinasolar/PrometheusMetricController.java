package cn.harmonycloud.development.inbound.controller.trinasolar;


import cn.harmonycloud.development.pojo.vo.instance.CpuMetricsVO;
import cn.harmonycloud.development.pojo.vo.instance.DiskIoMetricsVO;
import cn.harmonycloud.development.pojo.vo.instance.MemoryMetricsVO;
import cn.harmonycloud.development.service.PrometheusCpuMetricsService;
import cn.harmonycloud.development.service.PrometheusDiskIoMetricsService;
import cn.harmonycloud.development.service.PrometheusMemoryMetricsService;
import cn.harmonycloud.common.core.base.BaseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "K8s集群管理")
@RestController
@RequestMapping("/api/prometheus/")
public class PrometheusMetricController {


    @Autowired
    private PrometheusMemoryMetricsService prometheusMemoryMetricsService;

    @Autowired
    private PrometheusCpuMetricsService prometheusCpuMetricsService;


    @Autowired
    private PrometheusDiskIoMetricsService prometheusDiskIoMetricsService;

    @ApiOperation("获取服务内存使用趋势（通过Prometheus）")
    @GetMapping("/memory/trend")
    BaseResult<MemoryMetricsVO> getServiceMemoryTrendFromPrometheus(
            @ApiParam(value = "集群名称") @RequestParam String clusterName,
            @ApiParam(value = "Prometheus服务器地址") @RequestParam(required = false) String prometheusUrl,
            @ApiParam(value = "命名空间", required = true) @RequestParam String namespace,
            @ApiParam(value = "服务名称", required = true) @RequestParam String podName,
            @ApiParam(value = "容器名称", required = true) @RequestParam String containerName,
            @ApiParam(value = "时间范围（分钟），默认60分钟") @RequestParam(defaultValue = "30") Integer timeRangeMinutes
    ) {
        return BaseResult.ok(prometheusMemoryMetricsService.getServiceMemoryTrendFromPrometheus(clusterName, prometheusUrl, namespace, podName, containerName, timeRangeMinutes));
    }




    @ApiOperation("获取服务CPU使用率趋势")
    @GetMapping("/cpu/trend")
    BaseResult<CpuMetricsVO> getServiceCpuTrend(
            @ApiParam(value = "集群名称") @RequestParam String clusterName,
            @ApiParam(value = "Prometheus服务器地址") @RequestParam(required = false) String prometheusUrl,
            @ApiParam(value = "命名空间", required = true) @RequestParam String namespace,
            @ApiParam(value = "服务名称", required = true) @RequestParam String podName,
            @ApiParam(value = "容器名称", required = true) @RequestParam String containerName,
            @ApiParam(value = "时间范围（分钟），默认60分钟") @RequestParam(defaultValue = "30") Integer timeRangeMinutes
    ){
        return BaseResult.ok(prometheusCpuMetricsService.getServiceCpuTrendFromPrometheus(clusterName,prometheusUrl, namespace, podName, containerName,timeRangeMinutes));
    };


    /**
     * 1:读取  2:写入
     * @param prometheusUrl
     * @param namespace
     * @param podName
     * @param timeRangeMinutes
     * @return
     */
    @ApiOperation("获取服务磁盘IO趋势 ")
    @GetMapping("/io/trend")
    BaseResult<DiskIoMetricsVO> getServiceDiskIoTrend(
            @ApiParam(value = "存储设备") @RequestParam(defaultValue = "/dev/dm-0") String device,
            @ApiParam(value = "集群名称") @RequestParam String clusterName,
            @ApiParam(value = "Prometheus服务器地址") @RequestParam(required = false) String prometheusUrl,
            @ApiParam(value = "命名空间", required = true) @RequestParam String namespace,
            @ApiParam(value = "服务名称", required = true) @RequestParam String podName,
            @ApiParam(value = "容器名称", required = true) @RequestParam String containerName,
            @ApiParam(value = "时间范围（分钟），默认60分钟") @RequestParam(defaultValue = "30") Integer timeRangeMinutes
           ){
        return BaseResult.ok(prometheusDiskIoMetricsService.getServiceDiskIoTrendFromPrometheus(device,clusterName,prometheusUrl, namespace, podName, containerName,timeRangeMinutes));
    }



}
