package cn.harmonycloud.development.inbound.controller.trinasolar;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.pojo.dto.thgn.OpenSourceComptBaselineRegisterDTO;
import cn.harmonycloud.development.pojo.vo.thgn.sca.OpenSourceComptBaselineRegister;
import cn.harmonycloud.development.service.OpenSourceComptBaselineRegisterService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 开源组件基线登记控制器
 *
 * <AUTHOR>
 */
@Tag(name = "开源组件基线登记")
@RestController
@RequestMapping("/opensource/baseline/register")
public class OpenSourceComptBaselineRegisterController {

    @Autowired
    private OpenSourceComptBaselineRegisterService baselineService;

    /**
     * 创建基线
     */
    @PostMapping
    @Operation(summary = "创建开源组件基线")
    public BaseResult<Boolean> create(@RequestBody OpenSourceComptBaselineRegister baseline) {
        boolean result = baselineService.saveBaseline(baseline);
        return result ? BaseResult.ok(true, "创建成功") : BaseResult.failed("创建失败");
    }


    /**
     * 分页查询基线列表
     */
    @GetMapping("/page")
    @Operation(summary = "分页查询基线列表")
    public BaseResult<Page<OpenSourceComptBaselineRegister>> page(
            @RequestParam(defaultValue = "1") Integer pageNo,
            @RequestParam(defaultValue = "10") Integer pageSize,
            OpenSourceComptBaselineRegister query) {
        Page<OpenSourceComptBaselineRegister> result = baselineService.page(
                new Page<>(pageNo, pageSize),
                new QueryWrapper<>(query)
        );
        return BaseResult.ok(result);
    }

    /**
     * 更新基线
     */
    @PutMapping
    @Operation(summary = "更新基线")
    public BaseResult<Boolean> update(@RequestBody OpenSourceComptBaselineRegister baseline) {
        boolean result = baselineService.updateBaseline(baseline);
        return result ? BaseResult.ok(true, "更新成功") : BaseResult.failed("更新失败");
    }

    /**
     * 删除基线（逻辑删除）
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除基线")
    public BaseResult<Boolean> delete(@PathVariable Long id) {
        boolean result = baselineService.removeById(id);
        return result ? BaseResult.ok(true, "删除成功") : BaseResult.failed("删除失败");
    }

    /**
     * 分页查询基线组件
     */
    @PostMapping("/baseline/page")
    @Operation(summary = "分页查询基线组件")
    public BaseResult<Page<OpenSourceComptBaselineRegisterDTO>> pageBaseline(
            @RequestParam(defaultValue = "1") Integer pageNo,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestBody OpenSourceComptBaselineRegister query) {
        Page<OpenSourceComptBaselineRegisterDTO> result = baselineService.pageQueryBaseline(pageNo, pageSize, query);
        return BaseResult.ok(result);
    }

    /**
     * 分页查询非基线组件
     */
    @PostMapping("/non-baseline/page")
    @Operation(summary = "分页查询非基线组件")
    public BaseResult<Page<OpenSourceComptBaselineRegister>> pageNonBaseline(
            @RequestParam(defaultValue = "1") Integer pageNo,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestBody OpenSourceComptBaselineRegister query) {
        Page<OpenSourceComptBaselineRegister> result = baselineService.pageQueryNonBaseline(pageNo, pageSize, query);
        return BaseResult.ok(result);
    }
}