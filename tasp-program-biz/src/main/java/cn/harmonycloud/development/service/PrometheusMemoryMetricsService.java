package cn.harmonycloud.development.service;

import cn.harmonycloud.development.pojo.vo.instance.MemoryMetricsVO;

/**
 * Prometheus内存指标服务接口
 */
public interface PrometheusMemoryMetricsService {

    /**
     * 从Prometheus获取服务内存使用趋势
     *
     * @param clusterName     集群名称
     * @param namespace         命名空间
     * @param podName        实例名称
     * @param timeRangeMinutes  时间范围（分钟）
     * @return 内存指标数据
     */
    MemoryMetricsVO getServiceMemoryTrendFromPrometheus(String clusterName,
                                                        String prometheusUrl,
                                                        String namespace,
                                                        String podName,
                                                        String containerName,
                                                        Integer timeRangeMinutes);
}
