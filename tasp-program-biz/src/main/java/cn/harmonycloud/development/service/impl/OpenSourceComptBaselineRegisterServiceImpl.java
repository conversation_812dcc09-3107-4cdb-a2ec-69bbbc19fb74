package cn.harmonycloud.development.service.impl;

import cn.harmonycloud.development.outbound.db.mapper.thgn.OpenSourceComptBaselineRegisterMapper;
import cn.harmonycloud.development.pojo.dto.thgn.OpenSourceComptBaselineRegisterDTO;
import cn.harmonycloud.development.pojo.vo.thgn.sca.OpenSourceComptBaselineRegister;
import cn.harmonycloud.development.service.OpenSourceComptBaselineRegisterService;
import cn.harmonycloud.development.service.trinasolar.OpenSourceComponentBaselineService;
import cn.harmonycloud.development.util.RequestUtils;
import cn.harmonycloud.trinasolar.model.entity.OpenSourceComponentBaseline;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.apache.maven.artifact.versioning.ComparableVersion;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class OpenSourceComptBaselineRegisterServiceImpl extends ServiceImpl<OpenSourceComptBaselineRegisterMapper, OpenSourceComptBaselineRegister> implements OpenSourceComptBaselineRegisterService {

    public static final int IS_BASELINE = 1;
    public static final int IS_USED = 1;
    @Resource
    private OpenSourceComponentBaselineService openSourceComponentBaselineService;


    @Override
    public Page pageQueryBaseline(Integer pageNo, Integer pageSize, OpenSourceComptBaselineRegister query) {
        Page registerPage = getOpenSourceComptBaselineRegisterPage(pageNo, pageSize, query);
        if (registerPage.getRecords().isEmpty()) {
            synchronized (this) {
                List<OpenSourceComponentBaseline> openSourceComponentBaselines = openSourceComponentBaselineService.list();
                List<OpenSourceComptBaselineRegister> openSourceComptBaselineRegisters = new ArrayList<>();
                openSourceComponentBaselines.forEach(e -> {
                    OpenSourceComptBaselineRegister register = new OpenSourceComptBaselineRegister();
                    BeanUtils.copyProperties(e, register);
                    register.setId(null);
                    register.setIsBaseline(IS_BASELINE);
                    register.setIsUsed(IS_USED);
                    register.setAppId(query.getAppId());
                    openSourceComptBaselineRegisters.add(register);
                });
                saveBatch(openSourceComptBaselineRegisters);
                registerPage = getOpenSourceComptBaselineRegisterPage(pageNo, pageSize, query);
            }
        }
        List<OpenSourceComptBaselineRegister> records = registerPage.getRecords();
        List<OpenSourceComptBaselineRegisterDTO> openSourceComptBaselineRegisters = new ArrayList<>();
        records.forEach(e -> {
            OpenSourceComptBaselineRegisterDTO registerDTO = new OpenSourceComptBaselineRegisterDTO();
            BeanUtils.copyProperties(e, registerDTO);
            String version = registerDTO.getVersion();
            String useVersion = registerDTO.getUseVersion();
            //0 安全版本 | 1 版本过低 | 2 未知
            if (StringUtils.isEmpty(version) || StringUtils.isEmpty(useVersion)) {
                registerDTO.setStatus(2);
            }
            ComparableVersion v1 = new ComparableVersion(version);
            ComparableVersion v2 = new ComparableVersion(useVersion);
            int result = v1.compareTo(v2);
            if (result < 0) {
                registerDTO.setStatus(1);
            } else {
                registerDTO.setStatus(0);
            }
            openSourceComptBaselineRegisters.add(registerDTO);
        });
        registerPage.setRecords(openSourceComptBaselineRegisters);
        return registerPage;
    }

    private Page<OpenSourceComptBaselineRegister> getOpenSourceComptBaselineRegisterPage(Integer pageNo, Integer pageSize, OpenSourceComptBaselineRegister query) {
        Page<OpenSourceComptBaselineRegister> page = new Page<>(pageNo, pageSize);
        QueryWrapper<OpenSourceComptBaselineRegister> queryWrapper = new QueryWrapper<>(query);
        // 查询基线组件（假设表中有is_baseline字段，1表示基线组件）
        queryWrapper.eq("is_baseline", 1);
        // 排除已删除的数据
        queryWrapper.eq("del_flag", 0);
        // 按更新时间倒序排序
        queryWrapper.orderByDesc("update_time");
        return this.page(page, queryWrapper);
    }

    @Override
    public Page<OpenSourceComptBaselineRegister> pageQueryNonBaseline(Integer pageNo, Integer pageSize, OpenSourceComptBaselineRegister query) {
        Page<OpenSourceComptBaselineRegister> page = new Page<>(pageNo, pageSize);
        QueryWrapper<OpenSourceComptBaselineRegister> queryWrapper = new QueryWrapper<>(query);
        // 查询非基线组件（假设表中有is_baseline字段，0表示非基线组件）
        queryWrapper.eq("is_baseline", 0);
        // 排除已删除的数据
        queryWrapper.eq("del_flag", 0);
        // 按更新时间倒序排序
        queryWrapper.orderByDesc("update_time");
        return this.page(page, queryWrapper);
    }

    @Override
    public Boolean updateBaseline(OpenSourceComptBaselineRegister openSourceComptBaselineRegister) {
        String id = RequestUtils.getCurrentUser().getId();
        openSourceComptBaselineRegister.setUpdateBy(Long.parseLong(id));
        return updateById(openSourceComptBaselineRegister);
    }

    @Override
    public Boolean saveBaseline(OpenSourceComptBaselineRegister openSourceComptBaselineRegister) {
        openSourceComptBaselineRegister.setName(openSourceComptBaselineRegister.getName());
        LambdaQueryWrapper<OpenSourceComptBaselineRegister> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OpenSourceComptBaselineRegister::getName, openSourceComptBaselineRegister.getName());
        if (getOneOpt(queryWrapper).isPresent()) {
            throw new RuntimeException("名称不能重复:" + openSourceComptBaselineRegister.getName());
        }
        return save(openSourceComptBaselineRegister);
    }
}