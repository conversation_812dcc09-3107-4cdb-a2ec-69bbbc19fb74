package cn.harmonycloud.development.service.trinasolar.impl;

import cn.harmonycloud.development.outbound.thgn.ScaffoldTemplateRepository;
import cn.harmonycloud.trinasolar.model.entity.ScaffoldTemplate;
import cn.harmonycloud.development.pojo.vo.thgn.scaffold.ScaffoldTemplateVO;
import cn.harmonycloud.development.service.trinasolar.ScaffoldTemplateService;
import cn.hutool.core.collection.CollUtil;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;


/**
 * <AUTHOR>
 */
@Service
public class ScaffoldTemplateServiceImpl implements ScaffoldTemplateService {

    @Autowired
    private ScaffoldTemplateRepository scaffoldTemplateRepository;

    @Override
    public List<ScaffoldTemplateVO> list(List<String> techStacks) {
        List<ScaffoldTemplate> list = scaffoldTemplateRepository.lambdaQuery()
                .in(techStacks != null && !techStacks.isEmpty(), ScaffoldTemplate::getTechStack, techStacks)
                .ne(ScaffoldTemplate::getId, 0)
                .list();
        // 转对象9
        List<ScaffoldTemplateVO> scaffoldTemplateVOS = CollUtil.newArrayList();
        for (ScaffoldTemplate scaffoldTemplate : list) {
            ScaffoldTemplateVO scaffoldTemplateVO = new ScaffoldTemplateVO();
            scaffoldTemplateVO.setId(scaffoldTemplate.getId());
            scaffoldTemplateVO.setName(scaffoldTemplate.getName());
            scaffoldTemplateVO.setDescription(scaffoldTemplate.getDescription());
            scaffoldTemplateVO.setGitlabProjectId(scaffoldTemplate.getGitlabProjectId());
            scaffoldTemplateVO.setGitRepoUrl(scaffoldTemplate.getGitRepoUrl());
            scaffoldTemplateVO.setCategory(scaffoldTemplate.getCategory());
            scaffoldTemplateVO.setDefaultBranch(scaffoldTemplate.getDefaultBranch());
            scaffoldTemplateVO.setVersion(scaffoldTemplate.getVersion());
            scaffoldTemplateVO.setPicUrl(scaffoldTemplate.getPicUrl());
            scaffoldTemplateVO.setTechStack(scaffoldTemplate.getTechStack());
            scaffoldTemplateVO.setAddComponent(scaffoldTemplate.getAddComponent());
            scaffoldTemplateVOS.add(scaffoldTemplateVO);
        }
        return scaffoldTemplateVOS;
    }


}
