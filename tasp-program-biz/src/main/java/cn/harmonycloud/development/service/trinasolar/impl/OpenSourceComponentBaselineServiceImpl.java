package cn.harmonycloud.development.service.trinasolar.impl;

import cn.harmonycloud.development.execption.thgn.AppException;
import cn.harmonycloud.development.outbound.db.mapper.thgn.OpenSourceComponentBaselineMapper;
import cn.harmonycloud.trinasolar.model.entity.OpenSourceComponentBaseline;
import cn.harmonycloud.development.pojo.query.OpenSourceComponentBaselineQuery;
import cn.harmonycloud.development.service.trinasolar.OpenSourceComponentBaselineService;
import cn.harmonycloud.development.util.RequestUtils;
import cn.harmonycloud.trinasolar.model.User;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description 开源组件基线Service实现类
 **/
@Service
public class OpenSourceComponentBaselineServiceImpl extends ServiceImpl<OpenSourceComponentBaselineMapper, OpenSourceComponentBaseline> implements OpenSourceComponentBaselineService {

    @Override
    public Page<OpenSourceComponentBaseline> pageQuery(OpenSourceComponentBaselineQuery query) {
        Integer pageNum = query.getPageNo();
        Integer pageSize = query.getPageSize();
        Page<OpenSourceComponentBaseline> page = new Page<>(pageNum, pageSize);
        QueryWrapper<OpenSourceComponentBaseline> queryWrapper = new QueryWrapper<>();
        // 名称模糊查询
        if (StringUtils.isNotBlank(query.getName())) {
            queryWrapper.like("name", query.getName());
        }
        // 分类精确查询
        if (StringUtils.isNotBlank(query.getCategory())) {
            queryWrapper.eq("category", query.getCategory());
        }
        queryWrapper.orderByDesc("update_time");
        // 其他查询条件可以继续添加
        return this.page(page, queryWrapper);
    }

    @Override
    public Boolean saveOpenSourceComponentBaseline(OpenSourceComponentBaseline openSourceComponentBaseline) {
        // 添加组件名称存在性检查
        if (exists(
                new QueryWrapper<OpenSourceComponentBaseline>()
                        .eq("UPPER(name)", openSourceComponentBaseline.getName().toUpperCase())
                        .eq("del_flag", 0)
        )) {
            throw new AppException("组件名称已存在");
        }
        User currentUser = RequestUtils.getCurrentUser();
        String id = currentUser.getId();
        openSourceComponentBaseline.setUpdateBy(Long.parseLong(id));
        openSourceComponentBaseline.setCreateBy(Long.parseLong(id));
        openSourceComponentBaseline.setManager(currentUser.getUserRealname());
        return save(openSourceComponentBaseline);
    }

    @Override
    public Boolean updateOpenSourceComponentBaselineById(OpenSourceComponentBaseline openSourceComponentBaseline) {
        User currentUser = RequestUtils.getCurrentUser();
        String id = currentUser.getId();
        openSourceComponentBaseline.setUpdateBy(Long.parseLong(id));
        openSourceComponentBaseline.setManager(currentUser.getUserRealname());
        return updateById(openSourceComponentBaseline);
    }

    @Override
    public Boolean removeOpenSourceComponentBaselineById(Long id) {
        User currentUser = RequestUtils.getCurrentUser();
        OpenSourceComponentBaseline openSourceComponentBaseline = getById(id);
        if (openSourceComponentBaseline != null) {
            openSourceComponentBaseline.setUpdateBy(Long.parseLong(currentUser.getId()));
            openSourceComponentBaseline.setManager(currentUser.getUserRealname());
            updateById(openSourceComponentBaseline);
        }
        return removeById(id);
    }

}