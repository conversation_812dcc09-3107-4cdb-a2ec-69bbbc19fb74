package cn.harmonycloud.development.service.trinasolar;

import cn.harmonycloud.trinasolar.model.entity.OpenSourceComponentBaseline;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import cn.harmonycloud.development.pojo.query.OpenSourceComponentBaselineQuery;

public interface OpenSourceComponentBaselineService extends IService<OpenSourceComponentBaseline> {
    Page<OpenSourceComponentBaseline> pageQuery(OpenSourceComponentBaselineQuery query);

    Boolean saveOpenSourceComponentBaseline(OpenSourceComponentBaseline openSourceComponentBaseline);


    Boolean updateOpenSourceComponentBaselineById(OpenSourceComponentBaseline openSourceComponentBaseline);


    Boolean removeOpenSourceComponentBaselineById(Long id);
}