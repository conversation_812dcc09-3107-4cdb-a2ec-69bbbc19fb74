package cn.harmonycloud.development.service.impl;

import cn.harmonycloud.common.core.exception.BusinessException;
import cn.harmonycloud.development.config.K8sClusterConfig;
import cn.harmonycloud.development.service.K8sClusterConfigService;
import cn.harmonycloud.development.service.PrometheusCpuMetricsService;
import cn.harmonycloud.development.pojo.vo.instance.CpuMetricsVO;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Prometheus CPU指标服务实现类
 */
@Service
@Slf4j
public class PrometheusCpuMetricsServiceImpl implements PrometheusCpuMetricsService {


    @Autowired
    private K8sClusterConfigService k8sClusterConfigService;

    private static final DateTimeFormatter DATE_TIME_FORMATTER =
            DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");

    @Override
    public CpuMetricsVO getServiceCpuTrendFromPrometheus(String clusterName,String prometheusUrl,String namespace,
                                                         String podName, String containerName,Integer timeRangeMinutes) {
        if(StrUtil.isEmpty(prometheusUrl)){
            List<K8sClusterConfig.Cluster> clusters = k8sClusterConfigService.getClusterByName(clusterName);
            System.out.println("clusters--->:{}"+JSONObject.toJSONString(clusters));
            List<K8sClusterConfig.Cluster> clusterFilter = clusters.stream().filter(cluster -> clusterName.equals(cluster.getName())).collect(Collectors.toList());
            if(CollUtil.isEmpty(clusterFilter)){
                throw new BusinessException("未找到对应的集群信息");
            }
            K8sClusterConfig.Cluster cluster = clusterFilter.get(0);
            prometheusUrl=cluster.getPrometheusUrl();
        }

        // 参数校验
        if (!StringUtils.hasText(prometheusUrl) || !StringUtils.hasText(namespace) ||
                !StringUtils.hasText(podName)|| !StringUtils.hasText(containerName)) {
            throw new BusinessException("参数不能为空");
        }

        Map<String, Object> paramMap = MapUtil.newHashMap();
        paramMap.put("prometheusUrl", prometheusUrl);
        paramMap.put("namespace", namespace);
        paramMap.put("podName", podName);
        paramMap.put("containerName", containerName);

        // 默认时间范围60分钟
        if (timeRangeMinutes == null || timeRangeMinutes <= 0) {
            timeRangeMinutes = 30;
        }

        try {
            // 创建返回对象
            CpuMetricsVO cpuMetricsVO = new CpuMetricsVO();
            cpuMetricsVO.setPodName(podName);

            // 设置时间范围
            long endTime = Instant.now().getEpochSecond();
            long startTime = endTime - (timeRangeMinutes * 60);
            cpuMetricsVO.setStartTime(startTime); // 转换为毫秒
            cpuMetricsVO.setEndTime(endTime); // 转换为毫秒

            // 构建Prometheus查询语句 (CPU使用率，单位为核数，需要转换为百分比)
            String query = String.format(
                    "rate(container_cpu_usage_seconds_total{namespace=\"%s\", pod=~\"%s\",container=\"%s\"}[5m])",
                    namespace, podName, containerName);

            // 获取数据点
            List<CpuMetricsVO.CpuDataPoint> dataPoints = queryPrometheusData(
                    prometheusUrl, query, startTime, endTime,paramMap);

            cpuMetricsVO.setDataPoints(dataPoints);
            return cpuMetricsVO;
        } catch (Exception e) {
            log.error("从Prometheus获取服务CPU趋势失败，prometheusUrl={}, namespace={}, podName={} ,containerName={}",
                    prometheusUrl, namespace, podName,containerName, e);
            throw new BusinessException("获取服务CPU趋势失败", e);
        }
    }

    /**
     * 查询Prometheus数据
     *
     * @param prometheusUrl Prometheus服务器地址
     * @param query         查询语句
     * @param start         开始时间
     * @param end           结束时间
     * @return CPU数据点列表
     */
    private List<CpuMetricsVO.CpuDataPoint> queryPrometheusData(String prometheusUrl, String query,
                                                                long start, long end,Map<String,Object> paramMap) throws IOException {
        List<CpuMetricsVO.CpuDataPoint> dataPoints = new ArrayList<>();

        // 构建查询URL
        String encodedQuery = URLEncoder.encode(query, StandardCharsets.UTF_8);
        String rangeQueryUrl = String.format("%s/api/v1/query_range?query=%s&start=%d&end=%d&step=30",
                prometheusUrl, encodedQuery, start, end);

        log.debug("Prometheus query URL: {}", rangeQueryUrl);

        // 发送HTTP请求
        URL url = new URL(rangeQueryUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.setConnectTimeout(5000);
        connection.setReadTimeout(30000);

        int responseCode = connection.getResponseCode();
        if (responseCode == HttpURLConnection.HTTP_OK) {
            // 解析响应
            try (InputStream inputStream = connection.getInputStream();
                 BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {

                // 读取响应内容
                String response = reader.lines().collect(Collectors.joining("\n"));
                log.debug("Prometheus API response: {}", response);

                // 解析JSON响应
                dataPoints = parsePrometheusResponse(response,paramMap);
            }
        } else {
            String errorMessage = String.format("Prometheus查询失败，响应码: %d", responseCode);
            log.error(errorMessage);
            throw new BusinessException(errorMessage);
        }

        return dataPoints;
    }

    /**
     * 解析Prometheus API响应
     *
     * @param response JSON格式的响应
     * @return CPU数据点列表
     */
    private List<CpuMetricsVO.CpuDataPoint> parsePrometheusResponse(String response,Map<String,Object> paramMap) {
        List<CpuMetricsVO.CpuDataPoint> dataPoints = new ArrayList<>();
        String pod = String.valueOf(paramMap.get("podName"));
        String container = String.valueOf(paramMap.get("containerName"));
        String namespace = String.valueOf(paramMap.get("namespace"));

        try {
            // 解析JSON响应
            JSONObject jsonResponse = JSON.parseObject(response);

            if ("success".equals(jsonResponse.getString("status"))) {
                JSONObject data = jsonResponse.getJSONObject("data");
                if (data != null && "matrix".equals(data.getString("resultType"))) {
                    JSONArray results = data.getJSONArray("result");

                    if (results != null) {
                        for (int i = 0; i < results.size(); i++) {
                            JSONObject result = results.getJSONObject(i);
                            JSONArray values = result.getJSONArray("values");
                            JSONObject metric = result.getJSONObject("metric");
                            // 筛选出匹配的pod和container的指标
                            if(!StrUtil.equals(metric.getString("pod"),pod) ||
                                    !StrUtil.equals(metric.getString("container"),container)||
                                    !StrUtil.equals(metric.getString("namespace"),namespace)){
                                continue;
                            }
                            if (values != null) {
                                for (int j = 0; j < values.size(); j++) {
                                    JSONArray valuePair = values.getJSONArray(j);

                                    if (valuePair != null && valuePair.size() == 2) {
                                        // 创建数据点
                                        CpuMetricsVO.CpuDataPoint dataPoint = new CpuMetricsVO.CpuDataPoint();

                                        // 获取时间戳（秒）并转换为毫秒
                                        long timestamp = valuePair.getLongValue(0) * 1000;
                                        dataPoint.setTimestamp(timestamp);

                                        // 获取CPU使用率（核数）并转换为百分比
                                        String valueStr = valuePair.getString(1);
                                        double cpuUsageInCores = Double.parseDouble(valueStr);
                                        // 假设每个容器的CPU限制为1核，实际使用中可能需要根据实际情况调整
                                        double cpuUsagePercentage = cpuUsageInCores * 100;
                                        dataPoint.setCpuUsagePercentage(Math.round(cpuUsagePercentage * 100.0) / 100.0); // 保留两位小数

                                        // 格式化时间字符串
                                        dataPoint.setTimeString(Instant.ofEpochMilli(timestamp)
                                                .atZone(ZoneId.systemDefault())
                                                .format(DATE_TIME_FORMATTER));

                                        dataPoints.add(dataPoint);
                                    }
                                }
                            }
                        }
                    }
                }
            } else {
                String error = jsonResponse.getString("error");
                log.error("Prometheus API返回错误: {}", error);
                throw new BusinessException("Prometheus API返回错误: " + error);
            }
        } catch (Exception e) {
            log.error("解析Prometheus响应失败: {}", e.getMessage(), e);
            throw new BusinessException("解析Prometheus响应失败: " + e.getMessage());
        }

        return dataPoints;
    }
}
