package cn.harmonycloud.development.service.impl;

import cn.harmonycloud.common.core.exception.BusinessException;
import cn.harmonycloud.development.config.K8sClusterConfig;
import cn.harmonycloud.development.outbound.db.mapper.thgn.ApplicationProgramMapper;
import cn.harmonycloud.development.outbound.db.mapper.thgn.TaspProjectConfigMapper;
import cn.harmonycloud.development.pojo.dto.thgn.ProgramPodDTO;
import cn.harmonycloud.trinasolar.model.entity.ApplicationProgram;
import cn.harmonycloud.trinasolar.model.entity.TaspProjectConfig;
import cn.harmonycloud.development.pojo.vo.instance.K8sServiceInstanceVO;
import cn.harmonycloud.development.pojo.vo.instance.PodLogVO;
import cn.harmonycloud.development.pojo.vo.thgn.app.ApplicationProgramVO;
import cn.harmonycloud.development.service.K8sClusterConfigService;
import cn.harmonycloud.development.service.K8sLogService;
import cn.harmonycloud.development.service.K8sServiceInstanceService;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.fabric8.kubernetes.api.model.Pod;
import io.fabric8.kubernetes.api.model.PodList;
import io.fabric8.kubernetes.client.Config;
import io.fabric8.kubernetes.client.ConfigBuilder;
import io.fabric8.kubernetes.client.DefaultKubernetesClient;
import io.fabric8.kubernetes.client.KubernetesClient;
import io.fabric8.kubernetes.client.dsl.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class K8sLogServiceImpl implements K8sLogService {


    @Autowired
    private TaspProjectConfigMapper taspProjectConfigMapper;

    @Autowired
    private K8sClusterConfigService k8sClusterConfigService;


    @Value("${tasp.current.env:prod}")
    private String currentEnv;

    @Autowired
    private ApplicationProgramMapper applicationProgramMapper;

    @Autowired
    private K8sServiceInstanceService k8sServiceInstanceService;


    /**
     * 根据集群信息直接创建Kubernetes客户端
     *
     * @param masterUrl API Server地址
     * @param token 认证Token
     * @return Kubernetes客户端
     */
    private KubernetesClient createKubernetesClientDirect(String masterUrl, String token) {
        try {
            // 使用API Server地址和Token创建Kubernetes客户端
            Config config = new ConfigBuilder()
                    .withMasterUrl(masterUrl)
                    .withOauthToken(token)
                    .withTrustCerts(true)
                    .build();
            return new DefaultKubernetesClient(config);
        } catch (Exception e) {
            log.error("创建K8s客户端失败，masterUrl={}", masterUrl, e);
            throw new BusinessException("创建K8s客户端失败", e);
        }
    }

    @Override
    public PodLogVO getPodLogs(String clusterName,String masterUrl, String token, String namespace,
                               String podName, String containerName, Integer tailLines, Long sinceMinutes) {
        if(StrUtil.isEmpty(masterUrl) || StrUtil.isEmpty(token)){
            List<K8sClusterConfig.Cluster> clusters = k8sClusterConfigService.getClusterByName(clusterName);
            System.out.println("clusters--->:{}"+JSONObject.toJSONString(clusters));
            List<K8sClusterConfig.Cluster> clusterFilter = clusters.stream().filter(cluster -> clusterName.equals(cluster.getName())).collect(Collectors.toList());
            if(CollUtil.isEmpty(clusterFilter)){
                throw new BusinessException("未找到对应的集群信息");
            }
            K8sClusterConfig.Cluster cluster = clusterFilter.get(0);
            masterUrl=cluster.getServer();
            token=cluster.getUserToken();
        }
        // 参数校验
        if (!StringUtils.hasText(masterUrl) || !StringUtils.hasText(token) ||
                !StringUtils.hasText(namespace) || !StringUtils.hasText(podName)) {
            throw new BusinessException("参数不能为空");
        }

        try (KubernetesClient client = createKubernetesClientDirect(masterUrl, token)) {
            String sinceTime = null;
            String logContent="";
            if (Objects.nonNull(sinceMinutes)) {
                Instant timePoint = Instant.now().minusSeconds(sinceMinutes * 60);
                sinceTime = DateTimeFormatter.ISO_INSTANT.format(timePoint);
            }
            TailPrettyLoggable logOperation = client.pods()
                    .inNamespace(namespace)
                    .withName(podName)
                    .inContainer(containerName)
                    .sinceTime(sinceTime);
            // 只有当tailLines不为null时才应用行数限制
            if (tailLines != null) {
                PrettyLoggable prettyLoggable = logOperation.tailingLines(tailLines);
                logContent = prettyLoggable.getLog();
            } else {
                logContent = logOperation.getLog();
            }

            // 构建返回结果
            PodLogVO podLogVO = new PodLogVO();
            podLogVO.setPodName(podName);
            podLogVO.setContainerName(containerName);
            podLogVO.setLogContent(logContent);
            podLogVO.setTimestamp(System.currentTimeMillis());

            return podLogVO;
        } catch (Exception e) {
            log.error("获取Pod日志失败，masterUrl={}, namespace={}, podName={}",
                    masterUrl, namespace, podName, e);
            throw new BusinessException("获取Pod日志失败", e);
        }
    }

    @Override
    public String envNamespace(Long applicationId, String env) {
        QueryWrapper<TaspProjectConfig> qw = new QueryWrapper<>();
        qw.lambda().eq(TaspProjectConfig::getProjectId, applicationId)
                .eq(TaspProjectConfig::getConfigKey, "PROJECT_DEVOPS_ENV_NAMESPACE");
        TaspProjectConfig config = taspProjectConfigMapper.selectOne(qw);
        log.info("TaspProjectConfig :{}",config);
        if(Objects.isNull(config)){
            throw new BusinessException("请先维护应用系统在环境下的命名空间");
        }
        String configContent = config.getConfigContent();
        if(StrUtil.isEmpty(configContent)){
            throw new BusinessException("请先维护应用系统在环境下的命名空间");
        }
        Map map = JSONObject.parseObject(configContent, Map.class);
        log.info("map :{}",JSONObject.toJSONString(map));
        return String.valueOf(map.get(env.toUpperCase()));
    }

    @Override
    public Map<String, Object> programEnvPod( ProgramPodDTO programPodDTO) {
        if (Objects.isNull(programPodDTO)) {
            throw new BusinessException("参数不能为空");
        }
        Map<String, Object> map = MapUtil.newHashMap();
        List<Long> programIds = programPodDTO.getProgramIds();
        String env = currentEnv;
        if(StrUtil.isNotEmpty(programPodDTO.getEnv())){
            env=programPodDTO.getEnv();
        }
        log.info("programIds :{}", programIds);
        if (CollUtil.isEmpty(programIds)) {
            throw new BusinessException("应用程序id为空");
        }
        QueryWrapper<ApplicationProgram> qw = new QueryWrapper<>();
        qw.lambda().in(ApplicationProgram::getId, programIds);
        List<ApplicationProgram> applicationPrograms = applicationProgramMapper.selectList(qw);
        log.info("applicationPrograms :{}", JSONObject.toJSONString(applicationPrograms));
        if (CollUtil.isEmpty(applicationPrograms)) {
            throw new BusinessException("未获取到应用程序");
        }
        ApplicationProgram applicationProgram = applicationPrograms.get(0);
        //根据应用系统和环境获取命名空间
        String namespace="";
        try{
            namespace = envNamespace(applicationProgram.getApplicationId(), env);
        }catch (Exception e){
            log.error("获取命名空间失败",e);
        }
        if(StrUtil.isEmpty(namespace)){
            return map;
        }
        List<K8sClusterConfig.Cluster> clusters = k8sClusterConfigService.getAllClusters();
        String finalEnv = env;
        List<K8sClusterConfig.Cluster> clusterFilter = clusters.stream().filter(cluster -> finalEnv.equals(cluster.getEnv())).collect(Collectors.toList());
        log.info("clusterFilter:{}", JSONObject.toJSONString(clusterFilter));
        if(CollUtil.isEmpty(clusterFilter)){
            throw new BusinessException("未找到对应的集群信息");
        }
        K8sClusterConfig.Cluster cluster = clusterFilter.get(0);
        String masterUrl=cluster.getServer();
        String token=cluster.getUserToken();
        String clusterName = cluster.getName();
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        String finalNamespace = namespace;
        for (int i = 0; i < applicationPrograms.size(); i++) {
            final int index = i;
            final ApplicationProgram ap = applicationPrograms.get(index);
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                List<K8sServiceInstanceVO> serviceInstancesDirect=CollUtil.newArrayList();
                try {
                     serviceInstancesDirect = k8sServiceInstanceService.getServiceInstancesDirect(
                            clusterName, masterUrl, token, finalNamespace, StrUtil.isEmpty(ap.getDeploymentServiceName()) ? ap.getProgramNameEn() : ap.getDeploymentServiceName());
                } catch (Exception e) {
                    log.error("Failed to get pod",
                            e);
                }
                map.put(String.valueOf(ap.getId()), serviceInstancesDirect.size());
            });
            futures.add(future);
        }
        // 等待所有任务完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        try {
            allFutures.join(); // 阻塞直到所有任务完成
            log.info("All tasks completed successfully");
        } catch (CompletionException e) {
            log.error("Error in one of the tasks", e);
        }
        return map;
    }

    @Override
    public Boolean applicationEnvNamespace(Map map) {
        if (CollUtil.isEmpty(map)) {
            return false;
        }
        String businessDomain = String.valueOf(map.get("businessDomain"));
        String application = String.valueOf(map.get("application"));
        String applicationId = String.valueOf(map.get("applicationId"));
        if (StrUtil.isEmpty(businessDomain) || StrUtil.isEmpty(application) || StrUtil.isEmpty(applicationId)) {
            throw new BusinessException("参数不能为空");
        }

        Map<String, Object> config = MapUtil.newHashMap();
        config.put("DEV", businessDomain +"--"+ application +"--"+ "dev");
        config.put("TEST", businessDomain +"--"+ application +"--"+ "test");
        config.put("PROD", businessDomain +"--"+application +"--"+"prod");
        config.put("UAT", businessDomain +"--"+ application +"--"+ "uat");

        TaspProjectConfig projectConfig = new TaspProjectConfig();
        projectConfig.setConfigKey("PROJECT_DEVOPS_ENV_NAMESPACE");
        projectConfig.setConfigContent(JSONObject.toJSONString(config));
        projectConfig.setConfigName("项目环境命名空间");
        projectConfig.setProjectId(Long.valueOf(applicationId));
        taspProjectConfigMapper.insert(projectConfig);
        return true;
    }


}
