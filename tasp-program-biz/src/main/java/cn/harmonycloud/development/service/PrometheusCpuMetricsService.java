package cn.harmonycloud.development.service;

import cn.harmonycloud.development.pojo.vo.instance.CpuMetricsVO;

/**
 * Prometheus CPU指标服务接口
 */
public interface PrometheusCpuMetricsService {

    /**
     * 从Prometheus获取服务CPU使用率趋势
     *
     * @param prometheusUrl     Prometheus服务器地址
     * @param namespace         命名空间
     * @param podName        服务名称
     *  @param containerName     容器名称
     * @param timeRangeMinutes  时间范围（分钟）
     * @return CPU指标数据
     */
    CpuMetricsVO getServiceCpuTrendFromPrometheus(String clusterName,
                                                  String prometheusUrl,
                                                  String namespace,
                                                  String podName,
                                                  String containerName,
                                                  Integer timeRangeMinutes);
}
