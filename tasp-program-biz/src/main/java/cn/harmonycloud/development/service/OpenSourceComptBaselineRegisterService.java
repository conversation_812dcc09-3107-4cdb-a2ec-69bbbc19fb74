package cn.harmonycloud.development.service;

import cn.harmonycloud.development.pojo.dto.thgn.OpenSourceComptBaselineRegisterDTO;
import cn.harmonycloud.development.pojo.vo.thgn.sca.OpenSourceComptBaselineRegister;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 开源组件基线登记Service接口
 */
public interface OpenSourceComptBaselineRegisterService extends IService<OpenSourceComptBaselineRegister> {

    /**
     * 分页查询基线组件
     *
     * @param pageNum  页码
     * @param pageSize 每页条数
     * @param query    查询条件
     * @return 基线组件分页列表
     */
    Page<OpenSourceComptBaselineRegisterDTO> pageQueryBaseline(Integer pageNum, Integer pageSize, OpenSourceComptBaselineRegister query);

    /**
     * 分页查询非基线组件
     *
     * @param pageNum  页码
     * @param pageSize 每页条数
     * @param query    查询条件
     * @return 非基线组件分页列表
     */
    Page<OpenSourceComptBaselineRegister> pageQueryNonBaseline(Integer pageNum, Integer pageSize, OpenSourceComptBaselineRegister query);


    Boolean updateBaseline(OpenSourceComptBaselineRegister openSourceComptBaselineRegister);


    Boolean saveBaseline(OpenSourceComptBaselineRegister openSourceComptBaselineRegister);

}