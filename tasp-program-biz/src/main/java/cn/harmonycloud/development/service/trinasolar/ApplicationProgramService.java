package cn.harmonycloud.development.service.trinasolar;

import cn.harmonycloud.development.pojo.dto.thgn.GitlabCommitCountDTO;
import cn.harmonycloud.development.pojo.dto.thgn.ApplicationCheckUserInAppDTO;
import cn.harmonycloud.development.pojo.dto.thgn.ApplicationProgramDTO;
import cn.harmonycloud.development.pojo.dto.thgn.ApplicationProgramDetailDTO;
import cn.harmonycloud.development.pojo.dto.thgn.ApplicationUserDTO;
import cn.harmonycloud.development.pojo.dto.thgn.*;
import cn.harmonycloud.development.pojo.vo.thgn.app.ApplicationProgramVO;
import cn.harmonycloud.trinasolar.model.PageResult;
import cn.harmonycloud.trinasolar.model.ScaffoldTemplateUseInfoRespDTO;
import cn.harmonycloud.trinasolar.model.TechStackRankRespDTO;
import cn.harmonycloud.trinasolar.model.entity.ApplicationProgram;
import cn.harmonycloud.trinasolar.model.vo.PipelineRespVO;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ApplicationProgramService {

    void modifyApplicationProgram(ApplicationProgramDTO applicationProgramDTO);

    ApplicationProgram createApplicationProgram(ApplicationProgramDTO applicationProgramDTO);

    Page<ApplicationProgramVO> listApplicationProgram(Long applicationId, String cnName, Page<ApplicationProgram> page, String technicalStackTags);


    List<ApplicationProgramVO> listAllApplicationProgram(Long applicationId);
    /**
     * 获取所有程序信息
     *
     * @param applicationId
     * @return
     */
    ApplicationCheckUserInAppDTO checkUserInApp(Long applicationId, Long userId);

    List<String> listTechStack(ApplicationProgramDTO applicationProgramDTO);

    ApplicationProgramDetailDTO applicationProgramDetail(Long programId);

    /**
     * @param programId 应用程序
     * @param projectId 应用系统id
     * @param appName   应用程序英文名称
     * @param userIds   用户的ids
     * @Param isDeveloper 是否开发者
     */
    Boolean addUserGrantToApplication(Long programId, Long projectId, String appName, List<Long> userIds, Boolean isDeveloper);

    /**
     * @param programId 应用程序
     * @param projectId 应用系统id
     * @param appName   应用程序英文名称
     * @param userIds   用户的ids
     */
    Boolean removeUserToApplication(Long programId, Long projectId, String appName, List<Long> userIds);


    void deleteApp(Long id);


    List<ApplicationUserDTO> listPersonnelByProgramId(Long programId);


    PageResult<PipelineRespVO> getPipelines(Long programId);


    JSONObject deploy(Long projectId, String pipelineId);

    Integer count();

    void deleteAllPrograms(Long id);

    List<String> getGitList(Long id);

    ScaffoldTemplateUseInfoRespDTO getScaffoldTemplateInfoRank(String startTime, String endTime);

    TechStackRankRespDTO getTechStackRank(String startTime, String endTime);

    List<GitlabCommitInfoDTO> getUserGitCommit(String startTime, String endTime, Long projectId);

    List<ProjectCommitInfoDTO> getUserSysCommit(String startTime, String endTime, String username, Long id);
}
