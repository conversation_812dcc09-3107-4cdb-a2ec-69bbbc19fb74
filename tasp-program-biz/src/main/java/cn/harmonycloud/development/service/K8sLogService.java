package cn.harmonycloud.development.service;

import cn.harmonycloud.development.pojo.dto.thgn.ProgramPodDTO;
import cn.harmonycloud.development.pojo.vo.instance.PodLogVO;
import java.util.List;
import java.util.Map;

public interface K8sLogService {

    /**
     * 获取指定Pod的日志
     *
     * @param masterUrl API Server地址
     * @param token 认证Token
     * @param namespace 命名空间
     * @param podName Pod名称
     * @param containerName 容器名称（可选）
     * @param tailLines 获取最后多少行日志（可选）
     * @param sinceMinutes 获取最近多少秒的日志（可选）
     * @return Pod日志信息
     */
    PodLogVO getPodLogs(String clusterName,String masterUrl, String token, String namespace,
                        String podName, String containerName, Integer tailLines, Long sinceMinutes);

    String envNamespace(Long applicationId, String env);

    Map<String, Object> programEnvPod(ProgramPodDTO programPodDTO);

    Boolean applicationEnvNamespace(Map map);
}
